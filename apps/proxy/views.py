from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.apps import apps
from django.http import HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from .router import proxy_router
import logging

logger = logging.getLogger(__name__)

class ProxyViewSet(viewsets.ViewSet):
    """ViewSet for managing proxy servers"""

    @action(detail=False, methods=['post'])
    def start(self, request):
        """Start a proxy server for a channel"""
        try:
            proxy_type = request.data.get('type', 'hls')
            channel_id = request.data.get('channel', 'default')
            url = request.data.get('url')

            if not url:
                return Response(
                    {'error': 'URL is required'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )

            proxy_app = apps.get_app_config('proxy')
            proxy_server = getattr(proxy_app, f'{proxy_type}_proxy')
            proxy_server.initialize_channel(url, channel_id)

            return Response({
                'message': f'{proxy_type.upper()} proxy started',
                'channel': channel_id,
                'url': url
            })

        except Exception as e:
            logger.error(f"Error starting proxy: {e}")
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def stop(self, request):
        """Stop a proxy server for a channel"""
        try:
            proxy_type = request.data.get('type', 'hls')
            channel_id = request.data.get('channel', 'default')

            proxy_app = apps.get_app_config('proxy')
            proxy_server = getattr(proxy_app, f'{proxy_type}_proxy')
            proxy_server.stop_channel(channel_id)

            return Response({
                'message': f'{proxy_type.upper()} proxy stopped',
                'channel': channel_id
            })

        except Exception as e:
            logger.error(f"Error stopping proxy: {e}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

@csrf_exempt
@require_http_methods(["GET"])
def unified_stream(request, channel_id):
    """
    Unified stream endpoint with intelligent format detection and routing

    This endpoint automatically detects the stream format and routes to the
    appropriate proxy (HLS or MPEGTS) based on:
    1. Stream profile preferences
    2. URL pattern analysis
    3. Content-type detection
    4. Actual content analysis
    """
    try:
        # Check for format override parameter
        force_format = request.GET.get('format')
        if force_format and force_format not in ['hls', 'mpegts']:
            return JsonResponse({'error': 'Invalid format parameter'}, status=400)

        # Route request through the proxy router
        return proxy_router.route_stream_request(request, channel_id, force_format)

    except Exception as e:
        logger.error(f"Error in unified stream endpoint for channel {channel_id}: {e}")
        return JsonResponse({'error': 'Internal server error'}, status=500)

@csrf_exempt
@require_http_methods(["GET"])
def stream_format_info(request, channel_id):
    """Get information about the detected stream format for a channel"""
    try:
        current_format = proxy_router.get_channel_format(channel_id)

        return JsonResponse({
            'channel_id': channel_id,
            'detected_format': current_format,
            'available_formats': ['hls', 'mpegts'],
            'can_switch': True
        })

    except Exception as e:
        logger.error(f"Error getting stream format info for channel {channel_id}: {e}")
        return JsonResponse({'error': 'Internal server error'}, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def force_format_switch(request, channel_id):
    """Force a channel to switch to a specific format"""
    try:
        import json
        data = json.loads(request.body)
        target_format = data.get('format')

        if not target_format:
            return JsonResponse({'error': 'Format parameter required'}, status=400)

        result = proxy_router.force_format_switch(channel_id, target_format)

        if result['success']:
            return JsonResponse(result)
        else:
            return JsonResponse(result, status=400)

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        logger.error(f"Error forcing format switch for channel {channel_id}: {e}")
        return JsonResponse({'error': 'Internal server error'}, status=500)