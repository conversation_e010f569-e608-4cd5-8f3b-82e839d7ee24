import logging
from typing import Di<PERSON>, <PERSON><PERSON>, Tu<PERSON>
from django.http import HttpResponse, JsonResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404
from apps.channels.models import Channel, Stream
from core.stream_detector import StreamDetector
from core.hls_config import HLSConfig
from .ts_proxy.server import ProxyServer as TSProxyServer
from .hls_proxy.server import HLSProxyServer
from .ts_proxy.views import stream_ts
from .hls_proxy.views import stream_endpoint as hls_stream_endpoint

logger = logging.getLogger(__name__)

class ProxyRouter:
    """Enhanced proxy router with intelligent stream detection and routing"""
    
    def __init__(self):
        self.ts_proxy = TSProxyServer()
        self.hls_proxy = HLSProxyServer()
        self.stream_detector = StreamDetector()
        self.hls_config = HLSConfig()
        
        # Cache for stream format detection to avoid repeated analysis
        self._format_cache = {}
        self._cache_max_size = 1000
    
    def route_stream_request(self, request, channel_id: str, force_format: str = None) -> HttpResponse:
        """
        Route stream request to appropriate proxy based on stream format detection
        
        Args:
            request: Django HTTP request
            channel_id: Channel UUID
            force_format: Optional format override ('hls' or 'mpegts')
            
        Returns:
            HttpResponse from appropriate proxy
        """
        try:
            # Get channel and stream information
            channel = get_object_or_404(Channel, uuid=channel_id)
            
            # Check if channel has a stream profile preference
            stream_profile = getattr(channel, 'stream_profile', None)
            profile_format = self._get_profile_format_preference(stream_profile)
            
            # Determine target format
            if force_format:
                target_format = force_format
                logger.info(f"Using forced format {target_format} for channel {channel_id}")
            elif profile_format:
                target_format = profile_format
                logger.info(f"Using profile format {target_format} for channel {channel_id}")
            else:
                # Auto-detect format
                target_format = self._detect_channel_format(channel)
                logger.info(f"Auto-detected format {target_format} for channel {channel_id}")
            
            # Route to appropriate proxy
            if target_format == 'hls':
                return self._route_to_hls_proxy(request, channel_id, channel)
            else:
                return self._route_to_ts_proxy(request, channel_id, channel)
                
        except Channel.DoesNotExist:
            logger.error(f"Channel {channel_id} not found")
            return JsonResponse({'error': 'Channel not found'}, status=404)
        except Exception as e:
            logger.error(f"Error routing stream request for channel {channel_id}: {e}")
            return JsonResponse({'error': 'Internal server error'}, status=500)
    
    def _get_profile_format_preference(self, stream_profile) -> Optional[str]:
        """Get format preference from stream profile"""
        if not stream_profile:
            return None
        
        profile_name = getattr(stream_profile, 'name', '').lower()
        
        if 'hls' in profile_name:
            return 'hls'
        elif profile_name in ['proxy', 'mpegts']:
            return 'mpegts'
        
        # Check if profile has output format specified
        output_format = getattr(stream_profile, 'output_format', '').lower()
        if output_format in ['hls', 'mpegts']:
            return output_format
        
        return None
    
    def _detect_channel_format(self, channel: Channel) -> str:
        """Detect the best format for a channel based on its streams"""
        streams = channel.streams.all()
        
        if not streams:
            logger.warning(f"No streams found for channel {channel.uuid}, defaulting to mpegts")
            return 'mpegts'
        
        # Analyze all streams and determine best format
        hls_confidence_total = 0.0
        mpegts_confidence_total = 0.0
        stream_count = 0
        
        for stream in streams:
            # Check cache first
            cache_key = f"{stream.id}:{stream.url}"
            if cache_key in self._format_cache:
                detection_result = self._format_cache[cache_key]
            else:
                # Get user agent for detection
                user_agent = None
                if hasattr(stream, 'm3u_account') and stream.m3u_account:
                    user_agent_obj = stream.m3u_account.get_user_agent()
                    if user_agent_obj:
                        user_agent = user_agent_obj.user_agent
                
                # Detect stream format
                detection_result = self.stream_detector.detect_stream_format(
                    stream.url, 
                    user_agent=user_agent,
                    timeout=5  # Shorter timeout for routing decisions
                )
                
                # Cache result
                self._cache_detection_result(cache_key, detection_result)
            
            # Accumulate confidence scores
            if detection_result['format'] == 'hls':
                hls_confidence_total += detection_result['confidence']
            elif detection_result['format'] == 'mpegts':
                mpegts_confidence_total += detection_result['confidence']
            
            stream_count += 1
        
        # Calculate average confidence
        if stream_count > 0:
            avg_hls_confidence = hls_confidence_total / stream_count
            avg_mpegts_confidence = mpegts_confidence_total / stream_count
            
            logger.debug(f"Channel {channel.uuid} format analysis: "
                        f"HLS avg confidence: {avg_hls_confidence:.2f}, "
                        f"MPEGTS avg confidence: {avg_mpegts_confidence:.2f}")
            
            # Choose format with higher confidence
            if avg_hls_confidence > avg_mpegts_confidence and avg_hls_confidence > 0.5:
                return 'hls'
        
        # Default to MPEGTS for compatibility
        return 'mpegts'
    
    def _cache_detection_result(self, cache_key: str, result: Dict):
        """Cache detection result with size limit"""
        if len(self._format_cache) >= self._cache_max_size:
            # Remove oldest entries (simple FIFO)
            oldest_keys = list(self._format_cache.keys())[:100]
            for key in oldest_keys:
                del self._format_cache[key]
        
        self._format_cache[cache_key] = result
    
    def _route_to_hls_proxy(self, request, channel_id: str, channel: Channel) -> HttpResponse:
        """Route request to HLS proxy"""
        try:
            logger.info(f"Routing channel {channel_id} to HLS proxy")
            
            # Initialize HLS channel if not already done
            if not self._is_hls_channel_initialized(channel_id):
                success = self._initialize_hls_channel(channel)
                if not success:
                    logger.error(f"Failed to initialize HLS channel {channel_id}")
                    return JsonResponse({'error': 'Failed to initialize HLS stream'}, status=500)
            
            # Route to HLS proxy view
            return hls_stream_endpoint(request, channel_id)
            
        except Exception as e:
            logger.error(f"Error routing to HLS proxy for channel {channel_id}: {e}")
            return JsonResponse({'error': 'HLS proxy error'}, status=500)
    
    def _route_to_ts_proxy(self, request, channel_id: str, channel: Channel) -> HttpResponse:
        """Route request to TS proxy"""
        try:
            logger.info(f"Routing channel {channel_id} to TS proxy")
            
            # Route to existing TS proxy view
            return stream_ts(request, channel_id)
            
        except Exception as e:
            logger.error(f"Error routing to TS proxy for channel {channel_id}: {e}")
            return JsonResponse({'error': 'TS proxy error'}, status=500)
    
    def _is_hls_channel_initialized(self, channel_id: str) -> bool:
        """Check if HLS channel is already initialized"""
        return hasattr(self.hls_proxy, 'stream_managers') and channel_id in self.hls_proxy.stream_managers
    
    def _initialize_hls_channel(self, channel: Channel) -> bool:
        """Initialize HLS channel with first available stream"""
        try:
            streams = channel.streams.all()
            if not streams:
                logger.error(f"No streams available for channel {channel.uuid}")
                return False
            
            # Use first stream for initialization
            first_stream = streams[0]
            
            # Get user agent
            user_agent = None
            if hasattr(first_stream, 'm3u_account') and first_stream.m3u_account:
                user_agent_obj = first_stream.m3u_account.get_user_agent()
                if user_agent_obj:
                    user_agent = user_agent_obj.user_agent
            
            # Determine if transcoding is needed
            stream_profile = getattr(channel, 'stream_profile', None)
            transcode = stream_profile and not self._is_hls_proxy_profile(stream_profile)
            
            # Initialize HLS proxy
            success = self.hls_proxy.initialize_channel(
                stream_url=first_stream.url,
                channel_id=str(channel.uuid),
                user_agent=user_agent,
                transcode=transcode,
                stream_id=first_stream.id
            )
            
            if success:
                logger.info(f"Successfully initialized HLS channel {channel.uuid}")
            else:
                logger.error(f"Failed to initialize HLS channel {channel.uuid}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error initializing HLS channel {channel.uuid}: {e}")
            return False
    
    def _is_hls_proxy_profile(self, stream_profile) -> bool:
        """Check if stream profile is HLS Proxy (no transcoding needed)"""
        if not stream_profile:
            return False
        
        profile_name = getattr(stream_profile, 'name', '').lower()
        return 'hls proxy' in profile_name or profile_name == 'hls_proxy'
    
    def get_channel_format(self, channel_id: str) -> str:
        """Get the current format being used for a channel"""
        try:
            channel = get_object_or_404(Channel, uuid=channel_id)
            return self._detect_channel_format(channel)
        except:
            return 'unknown'
    
    def force_format_switch(self, channel_id: str, target_format: str) -> Dict[str, any]:
        """Force a channel to switch to a specific format"""
        try:
            if target_format not in ['hls', 'mpegts']:
                return {'success': False, 'error': 'Invalid format specified'}
            
            channel = get_object_or_404(Channel, uuid=channel_id)
            
            # Stop current proxy if running
            self._stop_channel_proxies(channel_id)
            
            # Initialize with target format
            if target_format == 'hls':
                success = self._initialize_hls_channel(channel)
            else:
                # TS proxy initialization is handled by existing views
                success = True
            
            return {
                'success': success,
                'channel_id': channel_id,
                'format': target_format,
                'message': f'Channel switched to {target_format} format'
            }
            
        except Exception as e:
            logger.error(f"Error forcing format switch for channel {channel_id}: {e}")
            return {'success': False, 'error': str(e)}
    
    def _stop_channel_proxies(self, channel_id: str):
        """Stop all proxy instances for a channel"""
        try:
            # Stop HLS proxy if running
            if hasattr(self.hls_proxy, 'stream_managers') and channel_id in self.hls_proxy.stream_managers:
                self.hls_proxy.stop_channel(channel_id)
            
            # Stop TS proxy if running
            if hasattr(self.ts_proxy, 'stream_managers') and channel_id in self.ts_proxy.stream_managers:
                self.ts_proxy.stop_channel(channel_id)
                
        except Exception as e:
            logger.warning(f"Error stopping proxies for channel {channel_id}: {e}")

# Global router instance
proxy_router = ProxyRouter()
