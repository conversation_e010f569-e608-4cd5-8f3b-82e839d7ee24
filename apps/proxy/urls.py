from django.urls import path, include
from . import views

app_name = 'proxy'

urlpatterns = [
    # Unified proxy endpoints with intelligent routing
    path('stream/<str:channel_id>', views.unified_stream, name='unified_stream'),
    path('format/<str:channel_id>', views.stream_format_info, name='format_info'),
    path('switch/<str:channel_id>', views.force_format_switch, name='format_switch'),

    # Specific proxy endpoints
    path('ts/', include('apps.proxy.ts_proxy.urls')),
    path('hls/', include('apps.proxy.hls_proxy.urls')),
]