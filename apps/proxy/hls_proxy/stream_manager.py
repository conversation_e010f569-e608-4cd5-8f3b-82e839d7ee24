import os
import time
import threading
import logging
import json
from typing import Dict, Optional, Set
from datetime import datetime
from core.hls_config import HLSConfig
from core.redis_client import RedisClient

logger = logging.getLogger(__name__)

class HLSStreamManager:
    """Enhanced HLS stream manager with /data/hls integration and Redis coordination"""
    
    def __init__(self, channel_id: str, initial_url: str, user_agent: str, 
                 redis_client, transcode: bool = False, hls_config: HLSConfig = None):
        self.channel_id = channel_id
        self.current_url = initial_url
        self.user_agent = user_agent
        self.redis_client = redis_client
        self.transcode = transcode
        self.running = True
        self.hls_config = hls_config or HLSConfig()
        
        # NEW: Set up /data/hls paths for this channel
        self.segment_path = self.hls_config.get_segment_path(channel_id)
        self.playlist_path = self.hls_config.get_playlist_path(channel_id)
        self.transcoding_path = self.hls_config.get_transcoding_path(channel_id)
        self.temp_path = self.hls_config.get_temp_path()
        self.cache_path = self.hls_config.get_cache_path()
        
        # HLS-specific properties
        self.master_playlist = None
        self.variant_playlists = {}
        self.segment_cache = {}
        self.current_sequence = 0
        self.last_activity = time.time()
        
        # Threading and synchronization
        self.lock = threading.Lock()
        self.client_manager = None
        self.proxy_server = None
        
        # Statistics
        self.stats = {
            'segments_served': 0,
            'bytes_served': 0,
            'clients_connected': 0,
            'last_segment_time': None,
            'playlist_updates': 0
        }
        
        if transcode:
            self._setup_transcoding()
        else:
            self._setup_passthrough()
    
    def _setup_passthrough(self):
        """Setup direct HLS passthrough with /data/hls paths"""
        logger.info(f"Setting up HLS passthrough for channel {self.channel_id}")
        self.fetch_thread = threading.Thread(target=self._fetch_master_playlist)
        self.fetch_thread.daemon = True
        self.fetch_thread.start()
    
    def _setup_transcoding(self):
        """Setup HLS transcoding with /data/hls paths"""
        logger.info(f"Setting up HLS transcoding for channel {self.channel_id}")
        
        # Create transcoding directories
        os.makedirs(os.path.join(self.transcoding_path, 'input'), exist_ok=True)
        os.makedirs(os.path.join(self.transcoding_path, 'output'), exist_ok=True)
        os.makedirs(os.path.join(self.transcoding_path, 'temp'), exist_ok=True)
        
        # Initialize transcoding process (will be implemented in transcoder.py)
        self.transcoder = None  # Will be set up in Phase 7
    
    def _fetch_master_playlist(self):
        """Fetch and process master playlist"""
        while self.running:
            try:
                import requests
                headers = {'User-Agent': self.user_agent}
                response = requests.get(self.current_url, headers=headers, timeout=10)
                
                if response.status_code == 200:
                    self.master_playlist = response.text
                    self._process_master_playlist(response.text)
                    
                    # Cache playlist to /data/hls/cache
                    cache_file = os.path.join(self.cache_path, f'{self.channel_id}_master.m3u8')
                    with open(cache_file, 'w') as f:
                        f.write(response.text)
                    
                    # Update Redis metadata
                    self._update_redis_metadata('active')
                else:
                    logger.warning(f"Failed to fetch master playlist: {response.status_code}")
                    self._update_redis_metadata('error')
                
                time.sleep(5)  # Update every 5 seconds
                
            except Exception as e:
                logger.error(f"Error fetching master playlist for {self.channel_id}: {e}")
                self._update_redis_metadata('error')
                time.sleep(10)  # Longer delay on error
    
    def _process_master_playlist(self, playlist_content: str):
        """Process master playlist and extract variant streams"""
        try:
            import m3u8
            playlist = m3u8.loads(playlist_content)
            
            # Process variant playlists
            for variant in playlist.playlists:
                variant_url = variant.absolute_uri
                if variant_url:
                    self.variant_playlists[variant.stream_info.bandwidth] = {
                        'url': variant_url,
                        'bandwidth': variant.stream_info.bandwidth,
                        'resolution': variant.stream_info.resolution,
                        'codecs': variant.stream_info.codecs
                    }
            
            logger.debug(f"Processed {len(self.variant_playlists)} variant playlists for {self.channel_id}")
            
        except Exception as e:
            logger.error(f"Error processing master playlist for {self.channel_id}: {e}")
    
    def _update_redis_metadata(self, state: str):
        """Update channel metadata in Redis"""
        try:
            metadata_key = f"hls_proxy:channel:{self.channel_id}:metadata"
            update_data = {
                'state': state,
                'last_update': str(time.time()),
                'segments_served': str(self.stats['segments_served']),
                'bytes_served': str(self.stats['bytes_served']),
                'clients_connected': str(self.stats['clients_connected'])
            }
            
            self.redis_client.hset(metadata_key, mapping=update_data)
            
        except Exception as e:
            logger.warning(f"Failed to update Redis metadata for {self.channel_id}: {e}")
    
    def get_master_playlist(self) -> Optional[str]:
        """Get the current master playlist"""
        if self.master_playlist:
            return self.master_playlist
        
        # Try to load from cache
        cache_file = os.path.join(self.cache_path, f'{self.channel_id}_master.m3u8')
        if os.path.exists(cache_file):
            with open(cache_file, 'r') as f:
                return f.read()
        
        return None
    
    def get_variant_playlist(self, bandwidth: int) -> Optional[str]:
        """Get a specific variant playlist"""
        if bandwidth in self.variant_playlists:
            variant_info = self.variant_playlists[bandwidth]
            # Fetch and return variant playlist
            try:
                import requests
                headers = {'User-Agent': self.user_agent}
                response = requests.get(variant_info['url'], headers=headers, timeout=10)
                
                if response.status_code == 200:
                    return response.text
                    
            except Exception as e:
                logger.error(f"Error fetching variant playlist {bandwidth} for {self.channel_id}: {e}")
        
        return None
    
    def update_url(self, new_url: str) -> bool:
        """Update stream URL with proper synchronization"""
        if new_url != self.current_url:
            with self.lock:
                old_url = self.current_url
                self.current_url = new_url
                
                # Clear cached data
                self.master_playlist = None
                self.variant_playlists.clear()
                self.segment_cache.clear()
                
                # Update Redis metadata
                metadata_key = f"hls_proxy:channel:{self.channel_id}:metadata"
                self.redis_client.hset(metadata_key, mapping={
                    'url': new_url,
                    'state': 'switching',
                    'last_update': str(time.time())
                })
                
                logger.info(f"Updated URL for channel {self.channel_id}: {old_url} -> {new_url}")
                return True
        
        return False
    
    def record_segment_served(self, segment_size: int):
        """Record statistics for served segment"""
        with self.lock:
            self.stats['segments_served'] += 1
            self.stats['bytes_served'] += segment_size
            self.stats['last_segment_time'] = time.time()
            self.last_activity = time.time()
    
    def record_client_activity(self, client_id: str):
        """Record client activity"""
        self.last_activity = time.time()
        if self.client_manager:
            self.client_manager.record_activity(client_id)
    
    def enable_cleanup(self):
        """Enable cleanup monitoring"""
        pass  # Placeholder for cleanup logic
    
    def start_cleanup_thread(self):
        """Start cleanup monitoring thread"""
        cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        cleanup_thread.start()
    
    def _cleanup_loop(self):
        """Cleanup old segments and cache files"""
        while self.running:
            try:
                current_time = time.time()
                max_age = self.hls_config.get_max_segment_age()
                
                # Clean up old segments
                if os.path.exists(self.segment_path):
                    for filename in os.listdir(self.segment_path):
                        filepath = os.path.join(self.segment_path, filename)
                        if os.path.isfile(filepath):
                            file_age = current_time - os.path.getmtime(filepath)
                            if file_age > max_age:
                                os.remove(filepath)
                                logger.debug(f"Cleaned up old segment: {filename}")
                
                # Clean up old cache files
                if os.path.exists(self.cache_path):
                    for filename in os.listdir(self.cache_path):
                        if filename.startswith(f'{self.channel_id}_'):
                            filepath = os.path.join(self.cache_path, filename)
                            if os.path.isfile(filepath):
                                file_age = current_time - os.path.getmtime(filepath)
                                if file_age > 3600:  # 1 hour for cache files
                                    os.remove(filepath)
                                    logger.debug(f"Cleaned up old cache file: {filename}")
                
                time.sleep(self.hls_config.get_cleanup_interval())
                
            except Exception as e:
                logger.error(f"Error in cleanup loop for {self.channel_id}: {e}")
                time.sleep(60)  # Wait a minute on error
    
    def stop(self):
        """Stop the stream manager"""
        self.running = False
        
        # Update Redis metadata
        try:
            metadata_key = f"hls_proxy:channel:{self.channel_id}:metadata"
            self.redis_client.hset(metadata_key, mapping={
                'state': 'stopped',
                'stop_time': str(time.time())
            })
        except Exception as e:
            logger.warning(f"Failed to update stop status in Redis: {e}")
        
        logger.info(f"Stopped HLS stream manager for channel {self.channel_id}")
