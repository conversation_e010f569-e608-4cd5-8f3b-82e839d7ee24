import time
import threading
import logging
import uuid
from typing import Dict, Set, Optional
from core.redis_client import RedisClient
from core.hls_config import HLSConfig

logger = logging.getLogger(__name__)

class HLSRedisKeys:
    """Redis key management for HLS proxy"""
    
    @staticmethod
    def channel_metadata(channel_id):
        return f"hls_proxy:channel:{channel_id}:metadata"
    
    @staticmethod
    def client_metadata(channel_id, client_id):
        return f"hls_proxy:channel:{channel_id}:clients:{client_id}"
    
    @staticmethod
    def clients(channel_id):
        return f"hls_proxy:channel:{channel_id}:clients"
    
    @staticmethod
    def segment_cache(channel_id, segment_id):
        return f"hls_proxy:channel:{channel_id}:segments:{segment_id}"
    
    @staticmethod
    def channel_paths(channel_id):
        return f"hls_proxy:channel:{channel_id}:paths"

class HLSClientManager:
    """Enhanced client manager with Redis coordination and /data/hls integration"""
    
    def __init__(self, channel_id: str, redis_client):
        self.channel_id = channel_id
        self.redis_client = redis_client
        self.clients: Set[str] = set()
        self.client_info: Dict[str, Dict] = {}
        self.lock = threading.Lock()
        self.hls_config = HLSConfig()
        
        # Store HLS paths for this channel
        self.segment_path = self.hls_config.get_segment_path(channel_id)
        self.playlist_path = self.hls_config.get_playlist_path(channel_id)
        
        # Statistics
        self.stats = {
            'total_clients': 0,
            'active_clients': 0,
            'total_requests': 0,
            'total_bytes_served': 0
        }
    
    def add_client(self, client_id: str = None, client_ip: str = None, user_agent: str = None) -> str:
        """Add a new HLS client with /data/hls path tracking"""
        if not client_id:
            client_id = str(uuid.uuid4())
        
        client_key = HLSRedisKeys.client_metadata(self.channel_id, client_id)
        client_data = {
            "user_agent": user_agent or "unknown",
            "ip_address": client_ip or "unknown",
            "connected_at": str(time.time()),
            "format": "hls",
            "segments_served": "0",
            "bytes_sent": "0",
            "last_active": str(time.time()),
            # NEW: Store client's HLS paths
            "segment_path": self.segment_path,
            "playlist_path": self.playlist_path
        }
        
        with self.lock:
            self.clients.add(client_id)
            self.client_info[client_id] = {
                'ip_address': client_ip or "unknown",
                'user_agent': user_agent or "unknown",
                'connected_at': time.time(),
                'last_active': time.time(),
                'segments_served': 0,
                'bytes_sent': 0
            }
            
            # Update Redis
            self.redis_client.hset(client_key, mapping=client_data)
            self.redis_client.sadd(HLSRedisKeys.clients(self.channel_id), client_id)
            self.redis_client.expire(client_key, 300)  # 5 minute TTL
            
            # Store channel paths in Redis
            paths_key = HLSRedisKeys.channel_paths(self.channel_id)
            self.redis_client.hset(paths_key, mapping={
                "segment_path": self.segment_path,
                "playlist_path": self.playlist_path,
                "transcoding_path": self.hls_config.get_transcoding_path(self.channel_id)
            })
            
            # Update statistics
            self.stats['total_clients'] += 1
            self.stats['active_clients'] = len(self.clients)
            
            logger.debug(f"Added HLS client {client_id} for channel {self.channel_id}")
        
        return client_id
    
    def remove_client(self, client_id: str):
        """Remove a client and clean up Redis data"""
        with self.lock:
            if client_id in self.clients:
                self.clients.remove(client_id)
                
                # Remove from Redis
                client_key = HLSRedisKeys.client_metadata(self.channel_id, client_id)
                self.redis_client.delete(client_key)
                self.redis_client.srem(HLSRedisKeys.clients(self.channel_id), client_id)
                
                # Update local info
                if client_id in self.client_info:
                    del self.client_info[client_id]
                
                # Update statistics
                self.stats['active_clients'] = len(self.clients)
                
                logger.debug(f"Removed HLS client {client_id} from channel {self.channel_id}")
    
    def record_activity(self, client_id: str, segment_size: int = 0):
        """Record client activity with segment serving statistics"""
        current_time = time.time()
        
        with self.lock:
            if client_id in self.client_info:
                client_info = self.client_info[client_id]
                client_info['last_active'] = current_time
                client_info['segments_served'] += 1
                client_info['bytes_sent'] += segment_size
                
                # Update Redis
                client_key = HLSRedisKeys.client_metadata(self.channel_id, client_id)
                self.redis_client.hset(client_key, mapping={
                    "last_active": str(current_time),
                    "segments_served": str(client_info['segments_served']),
                    "bytes_sent": str(client_info['bytes_sent'])
                })
                self.redis_client.expire(client_key, 300)  # Refresh TTL
                
                # Update global statistics
                self.stats['total_requests'] += 1
                self.stats['total_bytes_served'] += segment_size
    
    def get_client_count(self) -> int:
        """Get current number of active clients"""
        return len(self.clients)
    
    def get_client_info(self, client_id: str) -> Optional[Dict]:
        """Get information about a specific client"""
        with self.lock:
            return self.client_info.get(client_id)
    
    def get_all_clients(self) -> Dict[str, Dict]:
        """Get information about all clients"""
        with self.lock:
            return self.client_info.copy()
    
    def cleanup_inactive_clients(self, timeout: int = 300):
        """Clean up clients that haven't been active for the specified timeout"""
        current_time = time.time()
        inactive_clients = []
        
        with self.lock:
            for client_id, info in self.client_info.items():
                if current_time - info['last_active'] > timeout:
                    inactive_clients.append(client_id)
        
        for client_id in inactive_clients:
            self.remove_client(client_id)
            logger.debug(f"Cleaned up inactive client {client_id} from channel {self.channel_id}")
    
    def get_statistics(self) -> Dict:
        """Get client manager statistics"""
        with self.lock:
            return {
                **self.stats,
                'active_clients': len(self.clients),
                'client_list': list(self.clients)
            }
    
    def update_segment_cache(self, segment_id: str, segment_data: bytes):
        """Cache segment data in Redis for sharing across workers"""
        try:
            cache_key = HLSRedisKeys.segment_cache(self.channel_id, segment_id)
            self.redis_client.setex(cache_key, 60, segment_data)  # 1 minute cache
        except Exception as e:
            logger.warning(f"Failed to cache segment {segment_id}: {e}")
    
    def get_cached_segment(self, segment_id: str) -> Optional[bytes]:
        """Retrieve cached segment data from Redis"""
        try:
            cache_key = HLSRedisKeys.segment_cache(self.channel_id, segment_id)
            return self.redis_client.get(cache_key)
        except Exception as e:
            logger.warning(f"Failed to retrieve cached segment {segment_id}: {e}")
            return None
    
    def start_cleanup_thread(self):
        """Start background thread for client cleanup"""
        cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        cleanup_thread.start()
    
    def _cleanup_loop(self):
        """Background cleanup loop"""
        while True:
            try:
                self.cleanup_inactive_clients()
                time.sleep(60)  # Run cleanup every minute
            except Exception as e:
                logger.error(f"Error in client cleanup loop for channel {self.channel_id}: {e}")
                time.sleep(60)
    
    def stop(self):
        """Stop client manager and clean up all clients"""
        with self.lock:
            # Remove all clients
            for client_id in list(self.clients):
                self.remove_client(client_id)
            
            # Clean up channel-level Redis keys
            try:
                clients_key = HLSRedisKeys.clients(self.channel_id)
                paths_key = HLSRedisKeys.channel_paths(self.channel_id)
                self.redis_client.delete(clients_key, paths_key)
            except Exception as e:
                logger.warning(f"Failed to clean up Redis keys: {e}")
        
        logger.info(f"Stopped HLS client manager for channel {self.channel_id}")

class HLSStreamFetcher:
    """Enhanced stream fetcher with /data/hls integration"""
    
    def __init__(self, stream_manager, stream_buffer, hls_config: HLSConfig):
        self.stream_manager = stream_manager
        self.stream_buffer = stream_buffer
        self.hls_config = hls_config
        self.running = True
    
    def fetch_loop(self):
        """Main fetch loop for HLS segments"""
        logger.info(f"Starting HLS fetch loop for channel {self.stream_manager.channel_id}")
        
        while self.running and self.stream_manager.running:
            try:
                # Fetch master playlist and process variants
                master_playlist = self.stream_manager.get_master_playlist()
                if master_playlist:
                    self._process_variants(master_playlist)
                
                time.sleep(2)  # Fetch interval
                
            except Exception as e:
                logger.error(f"Error in HLS fetch loop: {e}")
                time.sleep(5)  # Error recovery delay
    
    def _process_variants(self, master_playlist: str):
        """Process variant playlists and fetch segments"""
        # This will be enhanced in later phases
        pass
    
    def stop(self):
        """Stop the fetch loop"""
        self.running = False
