services:
  dispatcharr:
    # build:
    #   context: ..
    #   dockerfile: docker/Dockerfile.dev
    image: ghcr.io/dispatcharr/dispatcharr:base
    container_name: dispatcharr_dev
    ports:
      - 5656:5656
      - 9191:9191
      - 8001:8001
    volumes:
      - ../:/app
      # NEW: HLS development volume (single consolidated volume)
      - hls_dev_data:/data/hls
      # - ./data/db:/data
    environment:
      - DISPATCHARR_ENV=dev
      - REDIS_HOST=localhost
      - CELERY_BROKER_URL=redis://localhost:6379/0
      - DISPATCHARR_LOG_LEVEL=debug
      # NEW: HLS development environment (all under /data/hls)
      - HLS_BASE_PATH=/data/hls
      - HLS_SEGMENT_PATH=/data/hls/segments
      - HLS_PLAYLIST_PATH=/data/hls/playlists
      - HLS_CACHE_PATH=/data/hls/cache
      - HLS_TEMP_PATH=/data/hls/temp
      - HLS_TRANSCODING_PATH=/data/hls/transcoding
      - HLS_LOGS_PATH=/data/hls/logs

  pgadmin:
    image: dpage/pgadmin4
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    volumes:
      - dispatcharr_dev_pgadmin:/var/lib/pgadmin
    ports:
      - 8082:80

  redis-commander:
    image: rediscommander/redis-commander:latest
    environment:
      - REDIS_HOSTS=dispatcharr:dispatcharr:6379:0
      - TRUST_PROXY=true
      - ADDRESS=0.0.0.0
    ports:
      - 8081:8081

volumes:
  dispatcharr_dev_pgadmin:
  # NEW: Single development HLS volume
  hls_dev_data:
    driver: local
