services:
  dispatcharr:
    # build:
    #   context: ..
    #   dockerfile: docker/Dockerfile.dev
    image: ghcr.io/dispatcharr/dispatcharr:base
    container_name: dispatcharr_dev
    ports:
      - 5656:5656
      - 9191:9191
      - 8001:8001
    volumes:
      - ../:/app
      # - ./data/db:/data
    environment:
      - DISPATCHARR_ENV=dev
      - REDIS_HOST=localhost
      - CELERY_BROKER_URL=redis://localhost:6379/0
      - DISPATCHARR_LOG_LEVEL=debug

  pgadmin:
    image: dpage/pgadmin4
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    volumes:
      - dispatcharr_dev_pgadmin:/var/lib/pgadmin
    ports:
      - 8082:80

  redis-commander:
    image: rediscommander/redis-commander:latest
    environment:
      - REDIS_HOSTS=dispatcharr:dispatcharr:6379:0
      - TRUST_PROXY=true
      - ADDRESS=0.0.0.0
    ports:
      - 8081:8081

volumes:
  dispatcharr_dev_pgadmin:
