#!/bin/bash

# Existing directories
mkdir -p /data/logos
mkdir -p /data/recordings
mkdir -p /data/uploads/m3us
mkdir -p /data/uploads/epgs
mkdir -p /data/m3us
mkdir -p /data/epgs
mkdir -p /app/logo_cache
mkdir -p /app/media

# NEW: Consolidated HLS directory structure under /data/hls
mkdir -p /data/hls
mkdir -p /data/hls/segments
mkdir -p /data/hls/playlists
mkdir -p /data/hls/cache
mkdir -p /data/hls/temp
mkdir -p /data/hls/transcoding
mkdir -p /data/hls/transcoding/input
mkdir -p /data/hls/transcoding/output
mkdir -p /data/hls/transcoding/temp
mkdir -p /data/hls/logs

sed -i "s/NGINX_PORT/${DISPATCHARR_PORT}/g" /etc/nginx/sites-enabled/default

# NOTE: mac doesn't run as root, so only manage permissions
# if this script is running as root
if [ "$(id -u)" = "0" ]; then
    # Needs to own ALL of /data except db, we handle that below
    chown -R $PUID:$PGID /data
    chown -R $PUID:$PGID /app

    # NEW: HLS-specific permissions (all under /data/hls)
    chown -R $PUID:$PGID /data/hls

    # Set proper permissions for HLS directories
    chmod 755 /data/hls
    chmod 755 /data/hls/segments
    chmod 755 /data/hls/playlists
    chmod 755 /data/hls/cache
    chmod 755 /data/hls/temp
    chmod 755 /data/hls/transcoding
    chmod 755 /data/hls/transcoding/input
    chmod 755 /data/hls/transcoding/output
    chmod 755 /data/hls/transcoding/temp
    chmod 755 /data/hls/logs

    # Permissions
    chown -R postgres:postgres /data/db
    chmod +x /data
fi

# NEW: Create HLS cleanup script
cat > /data/hls/cleanup.sh << 'EOF'
#!/bin/bash
# Clean up old HLS segments and playlists
find /data/hls/segments -name "*.ts" -mtime +1 -delete
find /data/hls/playlists -name "*.m3u8" -mtime +1 -delete
find /data/hls/temp -type f -mtime +1 -delete
find /data/hls/transcoding/temp -type f -mtime +1 -delete
find /data/hls/logs -name "*.log" -mtime +7 -delete
EOF

chmod +x /data/hls/cleanup.sh

# NEW: Create HLS configuration file
cat > /data/hls/config.json << 'EOF'
{
  "hls_settings": {
    "segment_duration": 6,
    "playlist_size": 10,
    "cleanup_interval": 3600,
    "max_segment_age": 86400,
    "transcoding_enabled": true,
    "cache_enabled": true
  },
  "paths": {
    "segments": "/data/hls/segments",
    "playlists": "/data/hls/playlists",
    "cache": "/data/hls/cache",
    "temp": "/data/hls/temp",
    "transcoding": "/data/hls/transcoding",
    "logs": "/data/hls/logs"
  }
}
EOF

chmod 644 /data/hls/config.json
