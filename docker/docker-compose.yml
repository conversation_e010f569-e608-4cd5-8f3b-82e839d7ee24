services:
  web:
    image: ghcr.io/dispatcharr/dispatcharr:latest
    container_name: dispatcharr_web
    ports:
      - 9191:9191
    depends_on:
      - db
      - redis
    volumes:
      # NEW: Single consolidated HLS volume
      - hls_data:/data/hls
    environment:
      - POSTGRES_HOST=db
      - POSTGRES_DB=dispatcharr
      - POSTGRES_USER=dispatch
      - POSTGRES_PASSWORD=secret
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
      - DISPATCHARR_LOG_LEVEL=info
      # NEW: HLS-specific environment variables (all under /data/hls)
      - HLS_BASE_PATH=/data/hls
      - HLS_SEGMENT_PATH=/data/hls/segments
      - HLS_PLAYLIST_PATH=/data/hls/playlists
      - HLS_CACHE_PATH=/data/hls/cache
      - HLS_TEMP_PATH=/data/hls/temp
      - HLS_TRANSCODING_PATH=/data/hls/transcoding
      - HLS_LOGS_PATH=/data/hls/logs
    # Optional for hardware acceleration
    #group_add:
    #  - video
    #  #- render # Uncomment if your GPU requires it
    #devices:
    #  - /dev/dri:/dev/dri  # For Intel/AMD GPU acceleration (VA-API)
    # Uncomment the following lines for NVIDIA GPU support
    # NVidia GPU support (requires NVIDIA Container Toolkit)
    #deploy:
    #  resources:
    #      reservations:
    #          devices:
    #              - driver: nvidia
    #                count: all
    #                capabilities: [gpu]

  celery:
    image: ghcr.io/dispatcharr/dispatcharr:latest
    container_name: dispatcharr_celery
    depends_on:
      - db
      - redis
    volumes:
      - ../:/app
      # NEW: HLS volume for celery worker
      - hls_data:/data/hls
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - POSTGRES_HOST=db
      - POSTGRES_DB=dispatcharr
      - POSTGRES_USER=dispatch
      - POSTGRES_PASSWORD=secret
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
      # NEW: HLS environment variables for celery (all under /data/hls)
      - HLS_BASE_PATH=/data/hls
      - HLS_SEGMENT_PATH=/data/hls/segments
      - HLS_PLAYLIST_PATH=/data/hls/playlists
      - HLS_CACHE_PATH=/data/hls/cache
      - HLS_TEMP_PATH=/data/hls/temp
      - HLS_TRANSCODING_PATH=/data/hls/transcoding
      - HLS_LOGS_PATH=/data/hls/logs
    command: >
      bash -c "
      cd /app &&
      celery -A dispatcharr worker -l info
      "

  db:
    image: postgres:14
    container_name: dispatcharr_db
    ports:
      - "5436:5432"
    environment:
      - POSTGRES_DB=dispatcharr
      - POSTGRES_USER=dispatch
      - POSTGRES_PASSWORD=secret
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:latest
    container_name: dispatcharr_redis

volumes:
  postgres_data:
  # NEW: Single HLS volume containing all HLS functionality
  hls_data:
    driver: local
