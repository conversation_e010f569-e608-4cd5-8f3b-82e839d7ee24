services:
  web:
    image: ghcr.io/dispatcharr/dispatcharr:latest
    container_name: dispatcharr_web
    ports:
      - 9191:9191
    depends_on:
      - db
      - redis
    environment:
      - POSTGRES_HOST=db
      - POSTGRES_DB=dispatcharr
      - POSTGRES_USER=dispatch
      - POSTGRES_PASSWORD=secret
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
      - DISPATCHARR_LOG_LEVEL=info
    # Optional for hardware acceleration
    #group_add:
    #  - video
    #  #- render # Uncomment if your GPU requires it
    #devices:
    #  - /dev/dri:/dev/dri  # For Intel/AMD GPU acceleration (VA-API)
    # Uncomment the following lines for NVIDIA GPU support
    # NVidia GPU support (requires NVIDIA Container Toolkit)
    #deploy:
    #  resources:
    #      reservations:
    #          devices:
    #              - driver: nvidia
    #                count: all
    #                capabilities: [gpu]

  celery:
    image: ghcr.io/dispatcharr/dispatcharr:latest
    container_name: dispatcharr_celery
    depends_on:
      - db
      - redis
    volumes:
      - ../:/app
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - POSTGRES_HOST=db
      - POSTGRES_DB=dispatcharr
      - POSTGRES_USER=dispatch
      - POSTGRES_PASSWORD=secret
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
    command: >
      bash -c "
      cd /app &&
      celery -A dispatcharr worker -l info
      "

  db:
    image: postgres:14
    container_name: dispatcharr_db
    ports:
      - "5436:5432"
    environment:
      - POSTGRES_DB=dispatcharr
      - POSTGRES_USER=dispatch
      - POSTGRES_PASSWORD=secret
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:latest
    container_name: dispatcharr_redis

volumes:
  postgres_data:
