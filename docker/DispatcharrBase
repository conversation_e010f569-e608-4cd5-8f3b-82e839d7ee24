FROM lscr.io/linuxserver/ffmpeg:latest

ENV DEBIAN_FRONTEND=noninteractive
ENV VIRTUAL_ENV=/dispatcharrpy
ENV PATH="$VIRTUAL_ENV/bin:$PATH"

# --- Install Python 3.13 and system dependencies ---
# Note: Hardware acceleration (VA-API, VDPAU, NVENC) already included in base ffmpeg image
RUN apt-get update && apt-get install --no-install-recommends -y \
    ca-certificates software-properties-common gnupg2 curl wget \
    && add-apt-repository ppa:deadsnakes/ppa \
    && apt-get update \
    && apt-get install --no-install-recommends -y \
    python3.13 python3.13-dev python3.13-venv \
    python-is-python3 python3-pip \
    libpcre3 libpcre3-dev libpq-dev procps \
    build-essential gcc pciutils \
    nginx streamlink \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# --- Create Python virtual environment ---
RUN python3.13 -m venv $VIRTUAL_ENV && $VIRTUAL_ENV/bin/pip install --upgrade pip

# --- Install Python dependencies ---
COPY requirements.txt /tmp/requirements.txt
RUN $VIRTUAL_ENV/bin/pip install --no-cache-dir -r /tmp/requirements.txt && rm /tmp/requirements.txt

# --- Set up Redis 7.x ---
RUN curl -fsSL https://packages.redis.io/gpg | gpg --dearmor -o /usr/share/keyrings/redis-archive-keyring.gpg && \
    echo "deb [signed-by=/usr/share/keyrings/redis-archive-keyring.gpg] https://packages.redis.io/deb $(lsb_release -cs) main" | \
    tee /etc/apt/sources.list.d/redis.list && \
    apt-get update && apt-get install -y redis-server && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# --- Set up PostgreSQL 14.x ---
RUN curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc | gpg --dearmor -o /usr/share/keyrings/postgresql-keyring.gpg && \
    echo "deb [signed-by=/usr/share/keyrings/postgresql-keyring.gpg] http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" | \
    tee /etc/apt/sources.list.d/pgdg.list && \
    apt-get update && apt-get install -y postgresql-14 postgresql-contrib-14 && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Create render group for hardware acceleration support with GID 109
RUN groupadd -r -g 109 render || true

ENTRYPOINT ["/app/docker/entrypoint.sh"]