[uwsgi]
; exec-before = python manage.py collectstatic --noinput
; exec-before = python manage.py migrate --noinput

; First run Redis availability check script once
exec-before = python /app/scripts/wait_for_redis.py

; Start Redis first
attach-daemon = redis-server
; Then start other services
attach-daemon = celery -A dispatcharr worker --autoscale=6,1
attach-daemon = celery -A dispatcharr beat
attach-daemon = daphne -b 0.0.0.0 -p 8001 dispatcharr.asgi:application
attach-daemon = cd /app/frontend && npm run dev

# Core settings
chdir = /app
module = scripts.debug_wrapper:application
virtualenv = /dispatcharrpy
master = true
env = DJANGO_SETTINGS_MODULE=dispatcharr.settings

socket = /app/uwsgi.sock
chmod-socket = 777
vacuum = true
die-on-term = true
static-map = /static=/app/static

# Worker configuration
workers = 1
threads = 8
enable-threads = true
lazy-apps = true

# HTTP server
http = 0.0.0.0:5656
http-keepalive = 1
buffer-size = 65536
http-timeout = 600

# Async mode (use gevent for high concurrency)
gevent = 100
async = 100

# Performance tuning
thunder-lock = true
log-4xx = true
log-5xx = true
disable-logging = false
log-buffering = 1024  # Add buffer size limit for logging

; Longer timeouts for debugging sessions
harakiri = 3600
socket-timeout = 3600
http-timeout = 3600


# Ignore unknown options
ignore-sigpipe = true
ignore-write-errors = true
disable-write-exception = true

# Debugging settings
py-autoreload = 1
honour-stdin = true

# Environment variables
env = PYTHONPATH=/app
env = PYTHONUNBUFFERED=1
env = PYDEVD_DISABLE_FILE_VALIDATION=1
env = PYTHONUTF8=1
env = PYTHONXOPT=-Xfrozen_modules=off
env = PYDEVD_DEBUG=1
env = DEBUGPY_LOG_DIR=/app/debugpy_logs

# Debugging control variables
env = WAIT_FOR_DEBUGGER=false
env = DEBUG_TIMEOUT=30

# Enable console logging (stdout)
log-master = true
# Enable strftime formatting for timestamps
logformat-strftime = true
log-date = %%Y-%%m-%%d %%H:%%M:%%S,000
# Use the environment variable in log format - ensure consistent formatting with other files
log-format = %(ftime) $(DISPATCHARR_LOG_LEVEL) uwsgi.requests Worker ID: %(wid) %(method) %(status) %(uri) %(msecs)ms