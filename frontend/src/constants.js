export const USER_LEVELS = {
  STREAMER: 0,
  STANDARD: 1,
  ADMIN: 10,
};

export const USER_LEVEL_LABELS = {
  [USER_LEVELS.STREAMER]: 'Streamer',
  [USER_LEVELS.STANDARD]: 'Standard User',
  [USER_LEVELS.ADMIN]: 'Admin',
};

export const NETWORK_ACCESS_OPTIONS = {
  M3U_EPG: {
    label: 'M3U / EPG Endpoints',
    description: 'Limit access to M3U, EPG, and HDHR URLs',
  },
  STREAMS: {
    label: 'Stream Endpoints',
    description:
      'Limit network access to stream URLs, including XC stream URLs',
  },
  XC_API: {
    label: 'XC API',
    description: 'Limit access to the XC API',
  },
  UI: {
    label: 'UI',
    description: 'Limit access to the Dispatcharr UI',
  },
};

export const PROXY_SETTINGS_OPTIONS = {
  buffering_timeout: {
    label: 'Buffering Timeout',
    description: 'Maximum time (in seconds) to wait for buffering before switching streams',
  },
  buffering_speed: {
    label: 'Buffering Speed',
    description: 'Speed threshold below which buffering is detected (1.0 = normal speed)',
  },
  redis_chunk_ttl: {
    label: 'Buffer Chunk TTL',
    description: 'Time-to-live for buffer chunks in seconds (how long stream data is cached)',
  },
  channel_shutdown_delay: {
    label: 'Channel Shutdown Delay',
    description: 'Delay in seconds before shutting down a channel after last client disconnects',
  },
  channel_init_grace_period: {
    label: 'Channel Initialization Grace Period',
    description: 'Grace period in seconds during channel initialization',
  },
};

export const REGION_CHOICES = [
  { value: 'ad', label: 'AD' },
  { value: 'ae', label: 'AE' },
  { value: 'af', label: 'AF' },
  { value: 'ag', label: 'AG' },
  { value: 'ai', label: 'AI' },
  { value: 'al', label: 'AL' },
  { value: 'am', label: 'AM' },
  { value: 'ao', label: 'AO' },
  { value: 'aq', label: 'AQ' },
  { value: 'ar', label: 'AR' },
  { value: 'as', label: 'AS' },
  { value: 'at', label: 'AT' },
  { value: 'au', label: 'AU' },
  { value: 'aw', label: 'AW' },
  { value: 'ax', label: 'AX' },
  { value: 'az', label: 'AZ' },
  { value: 'ba', label: 'BA' },
  { value: 'bb', label: 'BB' },
  { value: 'bd', label: 'BD' },
  { value: 'be', label: 'BE' },
  { value: 'bf', label: 'BF' },
  { value: 'bg', label: 'BG' },
  { value: 'bh', label: 'BH' },
  { value: 'bi', label: 'BI' },
  { value: 'bj', label: 'BJ' },
  { value: 'bl', label: 'BL' },
  { value: 'bm', label: 'BM' },
  { value: 'bn', label: 'BN' },
  { value: 'bo', label: 'BO' },
  { value: 'bq', label: 'BQ' },
  { value: 'br', label: 'BR' },
  { value: 'bs', label: 'BS' },
  { value: 'bt', label: 'BT' },
  { value: 'bv', label: 'BV' },
  { value: 'bw', label: 'BW' },
  { value: 'by', label: 'BY' },
  { value: 'bz', label: 'BZ' },
  { value: 'ca', label: 'CA' },
  { value: 'cc', label: 'CC' },
  { value: 'cd', label: 'CD' },
  { value: 'cf', label: 'CF' },
  { value: 'cg', label: 'CG' },
  { value: 'ch', label: 'CH' },
  { value: 'ci', label: 'CI' },
  { value: 'ck', label: 'CK' },
  { value: 'cl', label: 'CL' },
  { value: 'cm', label: 'CM' },
  { value: 'cn', label: 'CN' },
  { value: 'co', label: 'CO' },
  { value: 'cr', label: 'CR' },
  { value: 'cu', label: 'CU' },
  { value: 'cv', label: 'CV' },
  { value: 'cw', label: 'CW' },
  { value: 'cx', label: 'CX' },
  { value: 'cy', label: 'CY' },
  { value: 'cz', label: 'CZ' },
  { value: 'de', label: 'DE' },
  { value: 'dj', label: 'DJ' },
  { value: 'dk', label: 'DK' },
  { value: 'dm', label: 'DM' },
  { value: 'do', label: 'DO' },
  { value: 'dz', label: 'DZ' },
  { value: 'ec', label: 'EC' },
  { value: 'ee', label: 'EE' },
  { value: 'eg', label: 'EG' },
  { value: 'eh', label: 'EH' },
  { value: 'er', label: 'ER' },
  { value: 'es', label: 'ES' },
  { value: 'et', label: 'ET' },
  { value: 'fi', label: 'FI' },
  { value: 'fj', label: 'FJ' },
  { value: 'fk', label: 'FK' },
  { value: 'fm', label: 'FM' },
  { value: 'fo', label: 'FO' },
  { value: 'fr', label: 'FR' },
  { value: 'ga', label: 'GA' },
  { value: 'gb', label: 'GB' },
  { value: 'gd', label: 'GD' },
  { value: 'ge', label: 'GE' },
  { value: 'gf', label: 'GF' },
  { value: 'gg', label: 'GG' },
  { value: 'gh', label: 'GH' },
  { value: 'gi', label: 'GI' },
  { value: 'gl', label: 'GL' },
  { value: 'gm', label: 'GM' },
  { value: 'gn', label: 'GN' },
  { value: 'gp', label: 'GP' },
  { value: 'gq', label: 'GQ' },
  { value: 'gr', label: 'GR' },
  { value: 'gs', label: 'GS' },
  { value: 'gt', label: 'GT' },
  { value: 'gu', label: 'GU' },
  { value: 'gw', label: 'GW' },
  { value: 'gy', label: 'GY' },
  { value: 'hk', label: 'HK' },
  { value: 'hm', label: 'HM' },
  { value: 'hn', label: 'HN' },
  { value: 'hr', label: 'HR' },
  { value: 'ht', label: 'HT' },
  { value: 'hu', label: 'HU' },
  { value: 'id', label: 'ID' },
  { value: 'ie', label: 'IE' },
  { value: 'il', label: 'IL' },
  { value: 'im', label: 'IM' },
  { value: 'in', label: 'IN' },
  { value: 'io', label: 'IO' },
  { value: 'iq', label: 'IQ' },
  { value: 'ir', label: 'IR' },
  { value: 'is', label: 'IS' },
  { value: 'it', label: 'IT' },
  { value: 'je', label: 'JE' },
  { value: 'jm', label: 'JM' },
  { value: 'jo', label: 'JO' },
  { value: 'jp', label: 'JP' },
  { value: 'ke', label: 'KE' },
  { value: 'kg', label: 'KG' },
  { value: 'kh', label: 'KH' },
  { value: 'ki', label: 'KI' },
  { value: 'km', label: 'KM' },
  { value: 'kn', label: 'KN' },
  { value: 'kp', label: 'KP' },
  { value: 'kr', label: 'KR' },
  { value: 'kw', label: 'KW' },
  { value: 'ky', label: 'KY' },
  { value: 'kz', label: 'KZ' },
  { value: 'la', label: 'LA' },
  { value: 'lb', label: 'LB' },
  { value: 'lc', label: 'LC' },
  { value: 'li', label: 'LI' },
  { value: 'lk', label: 'LK' },
  { value: 'lr', label: 'LR' },
  { value: 'ls', label: 'LS' },
  { value: 'lt', label: 'LT' },
  { value: 'lu', label: 'LU' },
  { value: 'lv', label: 'LV' },
  { value: 'ly', label: 'LY' },
  { value: 'ma', label: 'MA' },
  { value: 'mc', label: 'MC' },
  { value: 'md', label: 'MD' },
  { value: 'me', label: 'ME' },
  { value: 'mf', label: 'MF' },
  { value: 'mg', label: 'MG' },
  { value: 'mh', label: 'MH' },
  { value: 'ml', label: 'ML' },
  { value: 'mm', label: 'MM' },
  { value: 'mn', label: 'MN' },
  { value: 'mo', label: 'MO' },
  { value: 'mp', label: 'MP' },
  { value: 'mq', label: 'MQ' },
  { value: 'mr', label: 'MR' },
  { value: 'ms', label: 'MS' },
  { value: 'mt', label: 'MT' },
  { value: 'mu', label: 'MU' },
  { value: 'mv', label: 'MV' },
  { value: 'mw', label: 'MW' },
  { value: 'mx', label: 'MX' },
  { value: 'my', label: 'MY' },
  { value: 'mz', label: 'MZ' },
  { value: 'na', label: 'NA' },
  { value: 'nc', label: 'NC' },
  { value: 'ne', label: 'NE' },
  { value: 'nf', label: 'NF' },
  { value: 'ng', label: 'NG' },
  { value: 'ni', label: 'NI' },
  { value: 'nl', label: 'NL' },
  { value: 'no', label: 'NO' },
  { value: 'np', label: 'NP' },
  { value: 'nr', label: 'NR' },
  { value: 'nu', label: 'NU' },
  { value: 'nz', label: 'NZ' },
  { value: 'om', label: 'OM' },
  { value: 'pa', label: 'PA' },
  { value: 'pe', label: 'PE' },
  { value: 'pf', label: 'PF' },
  { value: 'pg', label: 'PG' },
  { value: 'ph', label: 'PH' },
  { value: 'pk', label: 'PK' },
  { value: 'pl', label: 'PL' },
  { value: 'pm', label: 'PM' },
  { value: 'pn', label: 'PN' },
  { value: 'pr', label: 'PR' },
  { value: 'ps', label: 'PS' },
  { value: 'pt', label: 'PT' },
  { value: 'pw', label: 'PW' },
  { value: 'py', label: 'PY' },
  { value: 'qa', label: 'QA' },
  { value: 're', label: 'RE' },
  { value: 'ro', label: 'RO' },
  { value: 'rs', label: 'RS' },
  { value: 'ru', label: 'RU' },
  { value: 'rw', label: 'RW' },
  { value: 'sa', label: 'SA' },
  { value: 'sb', label: 'SB' },
  { value: 'sc', label: 'SC' },
  { value: 'sd', label: 'SD' },
  { value: 'se', label: 'SE' },
  { value: 'sg', label: 'SG' },
  { value: 'sh', label: 'SH' },
  { value: 'si', label: 'SI' },
  { value: 'sj', label: 'SJ' },
  { value: 'sk', label: 'SK' },
  { value: 'sl', label: 'SL' },
  { value: 'sm', label: 'SM' },
  { value: 'sn', label: 'SN' },
  { value: 'so', label: 'SO' },
  { value: 'sr', label: 'SR' },
  { value: 'ss', label: 'SS' },
  { value: 'st', label: 'ST' },
  { value: 'sv', label: 'SV' },
  { value: 'sx', label: 'SX' },
  { value: 'sy', label: 'SY' },
  { value: 'sz', label: 'SZ' },
  { value: 'tc', label: 'TC' },
  { value: 'td', label: 'TD' },
  { value: 'tf', label: 'TF' },
  { value: 'tg', label: 'TG' },
  { value: 'th', label: 'TH' },
  { value: 'tj', label: 'TJ' },
  { value: 'tk', label: 'TK' },
  { value: 'tl', label: 'TL' },
  { value: 'tm', label: 'TM' },
  { value: 'tn', label: 'TN' },
  { value: 'to', label: 'TO' },
  { value: 'tr', label: 'TR' },
  { value: 'tt', label: 'TT' },
  { value: 'tv', label: 'TV' },
  { value: 'tw', label: 'TW' },
  { value: 'tz', label: 'TZ' },
  { value: 'ua', label: 'UA' },
  { value: 'ug', label: 'UG' },
  { value: 'um', label: 'UM' },
  { value: 'us', label: 'US' },
  { value: 'uy', label: 'UY' },
  { value: 'uz', label: 'UZ' },
  { value: 'va', label: 'VA' },
  { value: 'vc', label: 'VC' },
  { value: 've', label: 'VE' },
  { value: 'vg', label: 'VG' },
  { value: 'vi', label: 'VI' },
  { value: 'vn', label: 'VN' },
  { value: 'vu', label: 'VU' },
  { value: 'wf', label: 'WF' },
  { value: 'ws', label: 'WS' },
  { value: 'ye', label: 'YE' },
  { value: 'yt', label: 'YT' },
  { value: 'za', label: 'ZA' },
  { value: 'zm', label: 'ZM' },
  { value: 'zw', label: 'ZW' },
];
