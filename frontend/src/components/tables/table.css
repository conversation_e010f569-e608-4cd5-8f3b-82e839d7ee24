* {
  /* box-sizing: border-box; */
}

html {
  font-family: sans-serif;
  /* font-size: 14px; */
}

.divTable {
  /* border: 1px solid lightgray; */
  /* width: fit-content; */
  /* display: flex;
    flex-direction: column; */
}

.tr {
  display: flex;
}

.table-striped .tbody .tr:hover {
  background-color: rgb(68, 68, 68);
}

.tr {
  /* width: fit-content;
    width: 100%;  */
  /* height: 30px; */
}

.th,
.td {
  /* box-shadow: inset 0 0 0 1px lightgray; */
  /* padding: 0.25rem; */
  padding-left: 4px;
  padding-right: 4px;
}

.th {
  /* padding: 2px 4px; */
  position: relative;
  font-weight: bold;
  text-align: center;
  /* height: 30px; */
}

.td {
  border-bottom: solid 1px rgb(68, 68, 68);
}

.divTable.table-size-compact .td {
  min-height: 28px;
  font-size: var(--mantine-font-size-sm);
}

.divTable.table-size-default .td {
  min-height: 40px;
  font-size: var(--mantine-font-size-md);
}

.divTable.table-size-large .td {
  min-height: 48px;
  font-size: var(--mantine-font-size-md);
}

.resizer {
  position: absolute;
  top: 0;
  height: 100%;
  width: 5px;
  background: rgba(0, 0, 0, 0.5);
  cursor: col-resize;
  user-select: none;
  touch-action: none;
}

.resizer.ltr {
  right: 0;
}

.resizer.rtl {
  left: 0;
}

.resizer.isResizing {
  background: blue;
  opacity: 1;
}

@media (hover: hover) {
  .resizer {
    opacity: 0;
  }

  *:hover>.resizer {
    opacity: 1;
  }
}

/* .table-striped .tbody .tr:nth-child(odd), */
.table-striped .tbody .tr-odd {
  background-color: #18181b;
}

/* .table-striped .tbody .tr:nth-child(even), */
.table-striped .tbody .tr-even {
  background-color: #27272A;
}

/* Style for rows with no streams */
.no-streams-row {
  background-color: rgba(239, 68, 68, 0.15) !important;
  box-shadow: inset 0 0 0 1px rgba(239, 68, 68, 0.3);
  /* Add subtle border */
}

/* Special hover effect for rows with no streams */
.table-striped .tbody .tr.no-streams-row:hover {
  background-color: rgba(239, 68, 68, 0.3) !important;
  /* Darker red on hover */
}

/* Prevent text selection when shift key is pressed */
.shift-key-active {
  cursor: pointer !important;
}

.shift-key-active *,
.shift-key-active .tr,
.shift-key-active .td,
.shift-key-active .tbody {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* Always allow text selection in editable elements */
.shift-key-active input,
.shift-key-active textarea,
.shift-key-active [contenteditable="true"],
.shift-key-active .table-input-header input {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  cursor: text !important;
}

/* Improve specificity and ensure text selection is disabled when shift is pressed */
.shift-key-active,
.shift-key-active * {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  cursor: pointer !important;
}

/* Add a visual indicator when shift is pressed */
.shift-key-active .tbody .tr {
  transition: background-color 0.1s;
}

.shift-key-active .tbody .tr:hover {
  background-color: rgba(68, 68, 68, 0.7) !important;
}

/* Always allow text selection in inputs even when shift is pressed */
.shift-key-active input,
.shift-key-active textarea,
.shift-key-active [contenteditable="true"],
.shift-key-active select,
.shift-key-active .mantine-Select-input,
.shift-key-active .mantine-MultiSelect-input,
.shift-key-active .table-input-header input {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  cursor: text !important;
}
