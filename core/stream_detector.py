import re
import requests
import logging
from urllib.parse import urlparse, parse_qs
from typing import Dict, Optional, Tuple
from django.conf import settings

logger = logging.getLogger(__name__)

class StreamDetector:
    """Enhanced stream detection for HLS and MPEGTS formats"""
    
    # HLS detection patterns
    HLS_URL_PATTERNS = [
        r'\.m3u8(\?.*)?$',           # Direct .m3u8 files
        r'/hls/',                    # HLS path indicator
        r'/master/',                 # Master playlist path
        r'/playlist/',               # Playlist path
        r'/live/',                   # Live streaming path (often HLS)
        r'hls=true',                 # HLS parameter
        r'format=hls',               # Format parameter
        r'output=hls',               # Output parameter
    ]
    
    # MPEGTS detection patterns
    MPEGTS_URL_PATTERNS = [
        r'\.ts(\?.*)?$',             # Direct .ts files
        r'\.mpegts(\?.*)?$',         # Direct .mpegts files
        r'/mpegts/',                 # MPEGTS path indicator
        r'format=mpegts',            # Format parameter
        r'output=mpegts',            # Output parameter
        r'format=ts',                # TS format parameter
    ]
    
    # Content-Type headers for HLS
    HLS_CONTENT_TYPES = [
        'application/vnd.apple.mpegurl',
        'application/x-mpegurl',
        'audio/mpegurl',
        'audio/x-mpegurl',
        'text/plain',  # Some servers serve m3u8 as text/plain
    ]
    
    # Content-Type headers for MPEGTS
    MPEGTS_CONTENT_TYPES = [
        'video/mp2t',
        'video/MP2T',
        'application/octet-stream',  # Generic binary, often used for TS
    ]
    
    @classmethod
    def detect_stream_format(cls, url: str, user_agent: str = None, timeout: int = 10) -> Dict[str, any]:
        """
        Detect stream format with comprehensive analysis
        
        Returns:
            Dict containing:
            - format: 'hls', 'mpegts', or 'unknown'
            - confidence: float (0.0 to 1.0)
            - method: detection method used
            - details: additional information
        """
        result = {
            'format': 'unknown',
            'confidence': 0.0,
            'method': 'none',
            'details': {}
        }
        
        try:
            # Step 1: URL pattern analysis
            url_result = cls._detect_by_url_pattern(url)
            if url_result['confidence'] > 0.7:
                return url_result
            
            # Step 2: HTTP header analysis
            header_result = cls._detect_by_headers(url, user_agent, timeout)
            if header_result['confidence'] > url_result['confidence']:
                result = header_result
            else:
                result = url_result
            
            # Step 3: Content analysis (if confidence still low)
            if result['confidence'] < 0.8:
                content_result = cls._detect_by_content(url, user_agent, timeout)
                if content_result['confidence'] > result['confidence']:
                    result = content_result
            
            # Step 4: Fallback logic
            if result['confidence'] < 0.5:
                result = cls._apply_fallback_logic(url, result)
            
            logger.info(f"Stream detection for {url}: {result['format']} (confidence: {result['confidence']:.2f}, method: {result['method']})")
            return result
            
        except Exception as e:
            logger.error(f"Error detecting stream format for {url}: {e}")
            return {
                'format': 'unknown',
                'confidence': 0.0,
                'method': 'error',
                'details': {'error': str(e)}
            }
    
    @classmethod
    def _detect_by_url_pattern(cls, url: str) -> Dict[str, any]:
        """Detect format based on URL patterns"""
        url_lower = url.lower()
        
        # Check HLS patterns
        hls_matches = 0
        for pattern in cls.HLS_URL_PATTERNS:
            if re.search(pattern, url_lower):
                hls_matches += 1
        
        # Check MPEGTS patterns
        mpegts_matches = 0
        for pattern in cls.MPEGTS_URL_PATTERNS:
            if re.search(pattern, url_lower):
                mpegts_matches += 1
        
        # Determine result
        if hls_matches > mpegts_matches:
            confidence = min(0.9, 0.5 + (hls_matches * 0.2))
            return {
                'format': 'hls',
                'confidence': confidence,
                'method': 'url_pattern',
                'details': {'hls_matches': hls_matches, 'mpegts_matches': mpegts_matches}
            }
        elif mpegts_matches > hls_matches:
            confidence = min(0.9, 0.5 + (mpegts_matches * 0.2))
            return {
                'format': 'mpegts',
                'confidence': confidence,
                'method': 'url_pattern',
                'details': {'hls_matches': hls_matches, 'mpegts_matches': mpegts_matches}
            }
        else:
            return {
                'format': 'unknown',
                'confidence': 0.0,
                'method': 'url_pattern',
                'details': {'hls_matches': hls_matches, 'mpegts_matches': mpegts_matches}
            }
    
    @classmethod
    def _detect_by_headers(cls, url: str, user_agent: str = None, timeout: int = 10) -> Dict[str, any]:
        """Detect format based on HTTP headers"""
        try:
            headers = {}
            if user_agent:
                headers['User-Agent'] = user_agent
            
            # Make HEAD request to get headers without downloading content
            response = requests.head(url, headers=headers, timeout=timeout, allow_redirects=True)
            content_type = response.headers.get('Content-Type', '').lower()
            
            # Check for HLS content types
            for hls_type in cls.HLS_CONTENT_TYPES:
                if hls_type.lower() in content_type:
                    return {
                        'format': 'hls',
                        'confidence': 0.8,
                        'method': 'http_headers',
                        'details': {'content_type': content_type, 'status_code': response.status_code}
                    }
            
            # Check for MPEGTS content types
            for mpegts_type in cls.MPEGTS_CONTENT_TYPES:
                if mpegts_type.lower() in content_type:
                    return {
                        'format': 'mpegts',
                        'confidence': 0.8,
                        'method': 'http_headers',
                        'details': {'content_type': content_type, 'status_code': response.status_code}
                    }
            
            return {
                'format': 'unknown',
                'confidence': 0.0,
                'method': 'http_headers',
                'details': {'content_type': content_type, 'status_code': response.status_code}
            }
            
        except Exception as e:
            logger.warning(f"Header detection failed for {url}: {e}")
            return {
                'format': 'unknown',
                'confidence': 0.0,
                'method': 'http_headers',
                'details': {'error': str(e)}
            }
    
    @classmethod
    def _detect_by_content(cls, url: str, user_agent: str = None, timeout: int = 10) -> Dict[str, any]:
        """Detect format by analyzing initial content"""
        try:
            headers = {}
            if user_agent:
                headers['User-Agent'] = user_agent
            
            # Download first 1KB to analyze content
            headers['Range'] = 'bytes=0-1023'
            response = requests.get(url, headers=headers, timeout=timeout)
            
            if response.status_code in [200, 206]:  # OK or Partial Content
                content = response.text if response.text else response.content.decode('utf-8', errors='ignore')
                
                # Check for HLS playlist markers
                hls_markers = ['#EXTM3U', '#EXT-X-VERSION', '#EXT-X-TARGETDURATION', '#EXTINF']
                hls_score = sum(1 for marker in hls_markers if marker in content.upper())
                
                if hls_score >= 2:
                    return {
                        'format': 'hls',
                        'confidence': min(0.95, 0.7 + (hls_score * 0.1)),
                        'method': 'content_analysis',
                        'details': {'hls_markers_found': hls_score, 'content_preview': content[:200]}
                    }
                
                # Check for MPEGTS binary markers
                if isinstance(response.content, bytes) and len(response.content) > 4:
                    # MPEGTS packets start with 0x47
                    if response.content[0] == 0x47:
                        return {
                            'format': 'mpegts',
                            'confidence': 0.9,
                            'method': 'content_analysis',
                            'details': {'binary_marker': 'mpegts_sync_byte'}
                        }
            
            return {
                'format': 'unknown',
                'confidence': 0.0,
                'method': 'content_analysis',
                'details': {'status_code': response.status_code}
            }
            
        except Exception as e:
            logger.warning(f"Content analysis failed for {url}: {e}")
            return {
                'format': 'unknown',
                'confidence': 0.0,
                'method': 'content_analysis',
                'details': {'error': str(e)}
            }
    
    @classmethod
    def _apply_fallback_logic(cls, url: str, current_result: Dict) -> Dict[str, any]:
        """Apply fallback logic when detection confidence is low"""
        # If URL contains common streaming indicators, assume MPEGTS as fallback
        streaming_indicators = ['stream', 'live', 'tv', 'channel']
        
        if any(indicator in url.lower() for indicator in streaming_indicators):
            return {
                'format': 'mpegts',
                'confidence': 0.3,
                'method': 'fallback_logic',
                'details': {'reason': 'streaming_indicators_found', 'previous_result': current_result}
            }
        
        return current_result
    
    @classmethod
    def is_hls_stream(cls, url: str, user_agent: str = None) -> bool:
        """Simple boolean check if stream is HLS"""
        result = cls.detect_stream_format(url, user_agent)
        return result['format'] == 'hls' and result['confidence'] > 0.5
    
    @classmethod
    def is_mpegts_stream(cls, url: str, user_agent: str = None) -> bool:
        """Simple boolean check if stream is MPEGTS"""
        result = cls.detect_stream_format(url, user_agent)
        return result['format'] == 'mpegts' and result['confidence'] > 0.5
