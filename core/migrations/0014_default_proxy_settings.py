# Generated by Django 5.1.6 on 2025-03-01 14:01

import json
from django.db import migrations
from django.utils.text import slugify


def preload_proxy_settings(apps, schema_editor):
    CoreSettings = apps.get_model("core", "CoreSettings")

    # Default proxy settings
    default_proxy_settings = {
        "buffering_timeout": 15,
        "buffering_speed": 1.0,
        "redis_chunk_ttl": 60,
        "channel_shutdown_delay": 0,
        "channel_init_grace_period": 5,
    }

    CoreSettings.objects.create(
        key=slugify("Proxy Settings"),
        name="Proxy Settings",
        value=json.dumps(default_proxy_settings),
    )


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0013_default_network_access_settings"),
    ]

    operations = [
        migrations.RunPython(preload_proxy_settings),
    ]
