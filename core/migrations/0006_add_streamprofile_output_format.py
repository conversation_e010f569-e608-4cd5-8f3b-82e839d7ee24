# Generated by Django for HLS output support

from django.db import migrations, models

def create_hls_proxy_profile(apps, schema_editor):
    """Create the HLS Proxy stream profile"""
    StreamProfile = apps.get_model("core", "StreamProfile")
    UserAgent = apps.get_model("core", "UserAgent")
    
    # Get default user agent
    try:
        default_user_agent = UserAgent.objects.first()
    except:
        default_user_agent = None
    
    # Create HLS Proxy profile if it doesn't exist
    hls_profile, created = StreamProfile.objects.get_or_create(
        name='HLS Proxy',
        defaults={
            'command': '',
            'parameters': '',
            'locked': True,
            'is_active': True,
            'user_agent': default_user_agent,
            'output_format': 'hls'
        }
    )
    
    if created:
        print("Created HLS Proxy stream profile")
    else:
        # Update existing profile to have HLS output format
        hls_profile.output_format = 'hls'
        hls_profile.save()
        print("Updated existing HLS Proxy profile")

def reverse_hls_proxy_profile(apps, schema_editor):
    """Remove the HLS Proxy stream profile"""
    StreamProfile = apps.get_model("core", "StreamProfile")
    try:
        hls_profile = StreamProfile.objects.get(name='HLS Proxy')
        hls_profile.delete()
        print("Removed HLS Proxy stream profile")
    except StreamProfile.DoesNotExist:
        pass

class Migration(migrations.Migration):

    dependencies = [
        ('core', '0005_streamprofile_locked_alter_streamprofile_command_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='streamprofile',
            name='output_format',
            field=models.CharField(
                choices=[
                    ('auto', 'Auto-detect'),
                    ('hls', 'HLS'),
                    ('mpegts', 'MPEGTS'),
                ],
                default='auto',
                help_text='Preferred output format for this profile',
                max_length=20
            ),
        ),
        migrations.RunPython(create_hls_proxy_profile, reverse_hls_proxy_profile),
    ]
