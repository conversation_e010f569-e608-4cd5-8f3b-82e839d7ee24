# Generated by Django 5.1.6 on 2025-03-01 14:01

from django.db import migrations
from django.utils.text import slugify


def preload_network_access_settings(apps, schema_editor):
    CoreSettings = apps.get_model("core", "CoreSettings")
    CoreSettings.objects.create(
        key=slugify("Network Access"),
        name="Network Access",
        value="{}",
    )


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0012_default_active_m3u_accounts"),
    ]

    operations = [
        migrations.RunPython(preload_network_access_settings),
    ]
