import os
import json
from django.conf import settings

class HLSConfig:
    """Centralized HLS configuration management"""
    
    @classmethod
    def get_base_path(cls):
        """Get HLS base path"""
        return getattr(settings, 'HLS_SETTINGS', {}).get('BASE_PATH', '/data/hls')
    
    @classmethod
    def get_segment_path(cls, channel_id=None):
        """Get HLS segments path, optionally for specific channel"""
        base_path = getattr(settings, 'HLS_SETTINGS', {}).get('SEGMENT_PATH', '/data/hls/segments')
        if channel_id:
            return os.path.join(base_path, str(channel_id))
        return base_path
    
    @classmethod
    def get_playlist_path(cls, channel_id=None):
        """Get HLS playlists path, optionally for specific channel"""
        base_path = getattr(settings, 'HLS_SETTINGS', {}).get('PLAYLIST_PATH', '/data/hls/playlists')
        if channel_id:
            return os.path.join(base_path, str(channel_id))
        return base_path
    
    @classmethod
    def get_cache_path(cls):
        """Get HLS cache path"""
        return getattr(settings, 'HLS_SETTINGS', {}).get('CACHE_PATH', '/data/hls/cache')
    
    @classmethod
    def get_temp_path(cls):
        """Get HLS temporary path"""
        return getattr(settings, 'HLS_SETTINGS', {}).get('TEMP_PATH', '/data/hls/temp')
    
    @classmethod
    def get_transcoding_path(cls, channel_id=None):
        """Get HLS transcoding path, optionally for specific channel"""
        base_path = getattr(settings, 'HLS_SETTINGS', {}).get('TRANSCODING_PATH', '/data/hls/transcoding')
        if channel_id:
            return os.path.join(base_path, str(channel_id))
        return base_path
    
    @classmethod
    def get_logs_path(cls):
        """Get HLS logs path"""
        return getattr(settings, 'HLS_SETTINGS', {}).get('LOGS_PATH', '/data/hls/logs')
    
    @classmethod
    def ensure_channel_directories(cls, channel_id):
        """Ensure all necessary directories exist for a channel"""
        directories = [
            cls.get_segment_path(channel_id),
            cls.get_playlist_path(channel_id),
            cls.get_transcoding_path(channel_id),
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        
        return directories
    
    @classmethod
    def get_config_file_path(cls):
        """Get HLS configuration file path"""
        return os.path.join(cls.get_base_path(), 'config.json')
    
    @classmethod
    def load_config(cls):
        """Load HLS configuration from file"""
        config_path = cls.get_config_file_path()
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                return json.load(f)
        return {}
    
    @classmethod
    def get_segment_duration(cls):
        """Get HLS segment duration"""
        return getattr(settings, 'HLS_SETTINGS', {}).get('SEGMENT_DURATION', 6)
    
    @classmethod
    def get_playlist_size(cls):
        """Get HLS playlist size"""
        return getattr(settings, 'HLS_SETTINGS', {}).get('PLAYLIST_SIZE', 10)
    
    @classmethod
    def get_cleanup_interval(cls):
        """Get HLS cleanup interval"""
        return getattr(settings, 'HLS_SETTINGS', {}).get('CLEANUP_INTERVAL', 3600)
    
    @classmethod
    def get_max_segment_age(cls):
        """Get maximum segment age"""
        return getattr(settings, 'HLS_SETTINGS', {}).get('MAX_SEGMENT_AGE', 86400)
